import cv2
import numpy as np
import os

def test_opencv_display():
    """测试OpenCV窗口显示功能"""
    print("开始测试OpenCV显示功能...")
    
    # 检查视频文件是否存在
    video_path = "视频/699b2d9941c677a2fc210350cce01c53.mp4"
    if not os.path.exists(video_path):
        print(f"视频文件不存在: {video_path}")
        return
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    print("视频文件打开成功")
    
    # 创建窗口
    cv2.namedWindow('Test Display', cv2.WINDOW_NORMAL)
    cv2.resizeWindow('Test Display', 800, 600)
    cv2.moveWindow('Test Display', 100, 100)
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("视频播放完毕")
            break
            
        frame_count += 1
        
        # 在帧上绘制一些测试内容
        cv2.putText(frame, f"Frame: {frame_count}", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # 绘制一个测试矩形
        cv2.rectangle(frame, (100, 100), (300, 200), (255, 0, 0), 3)
        cv2.putText(frame, "Test Box", (110, 130), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 显示帧
        cv2.imshow('Test Display', frame)
        
        # 检查按键
        key = cv2.waitKey(30) & 0xFF
        if key == ord('q'):
            print("用户按下q键，退出")
            break
        elif key == ord(' '):
            print("暂停，按任意键继续...")
            cv2.waitKey(0)
            
        # 只播放前100帧进行测试
        if frame_count >= 100:
            print("测试完成，播放了100帧")
            break
    
    cap.release()
    cv2.destroyAllWindows()
    print("测试结束")

if __name__ == "__main__":
    test_opencv_display()
