import torch
import torchvision.transforms as transforms
import numpy as np
import cv2
import logging
from .model_mobilvit  import mobile_vit_xx_small  # 导入自定义模型
from .model import Net

# class Extractor(object):
#     def __init__(self, model_path, use_cuda=True):
#         self.net = Net(reid=True)
#         self.device = "cuda" if torch.cuda.is_available() and use_cuda else "cpu"
#         state_dict = torch.load(model_path, map_location=lambda storage, loc: storage)['net_dict']
#         self.net.load_state_dict(state_dict)
#         logger = logging.getLogger("root.tracker")
#         logger.info("Loading weights from {}... Done!".format(model_path))
#         self.net.to(self.device)
#         self.size = (64, 128)
#         self.norm = transforms.Compose([
#             transforms.ToTensor(),
#             transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
#         ])
#
#
#
#     def _preprocess(self, im_crops):
#         """
#         TODO:
#             1. to float with scale from 0 to 1
#             2. resize to (64, 128) as Market1501 dataset did
#             3. concatenate to a numpy array
#             3. to torch Tensor
#             4. normalize
#         """
#         def _resize(im, size):
#             return cv2.resize(im.astype(np.float32)/255., size)
#
#         im_batch = torch.cat([self.norm(_resize(im, self.size)).unsqueeze(0) for im in im_crops], dim=0).float()
#         return im_batch
#
#
#     def __call__(self, im_crops):
#         im_batch = self._preprocess(im_crops)
#         with torch.no_grad():
#             im_batch = im_batch.to(self.device)
#             features = self.net(im_batch)
#         return features.cpu().numpy()
class Extractor:
    def __init__(self, model_path, use_cuda=True):
        # 初始化自定义MobileViT模型
        self.net = mobile_vit_xx_small(num_classes=128)  # 特征维度128
        state_dict = torch.load(model_path, map_location='cpu')

        # 移除分类层权重（如果训练时保存了分类层）
        state_dict = {k: v for k, v in state_dict.items() if not k.startswith('classifier')}

        # 加载权重
        self.net.load_state_dict(state_dict, strict=False)
        self.net = self.net.eval()

        # 设备配置
        self.device = "cuda" if use_cuda and torch.cuda.is_available() else "cpu"
        self.net.to(self.device)

    def __call__(self, im_crops):
        # 图像预处理（必须与训练时一致）
        processed_imgs = []
        for img in im_crops:
            img = cv2.resize(img, (64, 64))  # 调整到训练尺寸
            img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR转RGB + HWC转CHW
            img = img / 255.0  # 归一化到[0,1]
            img = torch.Tensor(img).unsqueeze(0)
            processed_imgs.append(img)

        # 批量推理
        im_batch = torch.cat(processed_imgs, dim=0).to(self.device)
        with torch.no_grad():
            features = self.net(im_batch)
        return features.cpu().numpy()

if __name__ == '__main__':
    img = cv2.imread("demo.jpg")[:,:,(2,1,0)]
    extr = Extractor("checkpoint/ckpt.t7")
    feature = extr(img)
    print(feature.shape)

