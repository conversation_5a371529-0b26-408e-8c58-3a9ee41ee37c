import torch
import cv2
import os
import numpy as np
import time
from datetime import datetime
from deep_sort.deep_sort import DeepSort
from deep_sort.configs.parser import get_config
from ultralytics import YOL<PERSON>
from collections import defaultdict


class YOLOv8SegDeepSORT:
    def __init__(self, config):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = YOLO(config["weights"]).to(self.device)
        self.class_names = self.model.names

        # 优化DeepSORT参数配置
        cfg = get_config()
        cfg.merge_from_file("deep_sort/configs/deep_sort.yaml")
        # 调整参数减少ID切换
        cfg.DEEPSORT.MAX_IOU_DISTANCE = 0.7  # 提高IOU匹配阈值
        cfg.DEEPSORT.MAX_AGE = 30  # 增加最大丢失帧数
        cfg.DEEPSORT.N_INIT = 5  # 增加初始化帧数
        
        self.tracker = DeepSort(
            model_path=cfg.DEEPSORT.REID_CKPT,
            max_dist=cfg.DEEPSORT.MAX_DIST,
            min_confidence=cfg.DEEPSORT.MIN_CONFIDENCE,
            nms_max_overlap=cfg.DEEPSORT.NMS_MAX_OVERLAP,
            max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
            max_age=cfg.DEEPSORT.MAX_AGE,
            n_init=cfg.DEEPSORT.N_INIT,
            nn_budget=cfg.DEEPSORT.NN_BUDGET,
            use_cuda=torch.cuda.is_available()
        )

        self.input_path = config["input"]
        self.output_path = config["output"]
        os.makedirs(os.path.dirname(self.output_path), exist_ok=True)

        # 跟踪参数
        self.color_dict = {}
        self.alpha = 0.3
        self.border_margin = 40  # 左右边界宽度
        self.valid_region = None  # 有效区域坐标(x_start, x_end)

        # 增强跟踪稳定性设置
        self.position_history = defaultdict(list)  # 跟踪每个ID的位置历史
        self.appearance_history = defaultdict(list)  # 跟踪每个ID的外观特征
        self.locked_ids = set()  # 已锁定的ID集合
        self.trajectories = defaultdict(list)  # 完整轨迹记录
        self.pending_ids = set()  # 待确认ID集合
        
        # 稳定性参数
        self.position_threshold = 8  # 更严格的位置变化阈值
        self.stable_frames_threshold = 10  # 更长的稳定帧数要求
        self.appearance_sim_threshold = 0.9  # 更高的外观相似度阈值
        self.lock_after_frames = 15  # 更长的锁定ID所需帧数
        self.min_trajectory_length = 5  # 最小有效轨迹长度
        self.motion_consistency_threshold = 0.8  # 运动一致性阈值

        # 统计相关
        self.recorded_ids = set()  # 记录已统计的ID (仅记录class_id=0的track_ids)
        self.total_count = 0  # 累计统计 (仅统计class_id=0)

    def set_valid_region(self, frame_width):
        """设置有效区域边界"""
        self.valid_region = (self.border_margin, frame_width - self.border_margin)

    def is_in_valid_region(self, x_center):
        """检查目标是否在有效区域内"""
        return self.valid_region[0] <= x_center <= self.valid_region[1]

    def is_track_stable(self, track_id, current_pos, appearance_feat=None):
        """
        增强版稳定性检查(位置+外观+运动)
        Args:
            track_id: 目标ID
            current_pos: 当前帧目标中心位置(x,y)
            appearance_feat: 当前帧目标外观特征(可选)
        Returns:
            bool: 目标是否稳定
        """
        # 已锁定的ID直接返回稳定
        if track_id in self.locked_ids:
            return True
            
        # 更新轨迹记录
        self.trajectories[track_id].append(current_pos)
        if len(self.trajectories[track_id]) > 20:  # 保留最近20个位置点
            self.trajectories[track_id].pop(0)
            
        # 检查轨迹长度是否足够
        if len(self.trajectories[track_id]) < self.min_trajectory_length:
            self.pending_ids.add(track_id)
            return False
            
        # 位置稳定性检查
        pos_history = self.position_history[track_id]
        if len(pos_history) >= self.stable_frames_threshold:
            last_pos = pos_history[-1]
            dx = abs(current_pos[0] - last_pos[0])
            dy = abs(current_pos[1] - last_pos[1])
            pos_stable = dx < self.position_threshold and dy < self.position_threshold
        else:
            pos_stable = False
            
        # 运动一致性检查
        motion_consistent = True
        if len(self.trajectories[track_id]) >= 3:
            # 计算运动方向一致性
            trajectory = np.array(self.trajectories[track_id])
            motion_vectors = np.diff(trajectory, axis=0)
            if len(motion_vectors) > 1:
                # 计算连续运动向量的余弦相似度
                cos_sims = []
                for i in range(len(motion_vectors)-1):
                    norm1 = np.linalg.norm(motion_vectors[i])
                    norm2 = np.linalg.norm(motion_vectors[i+1])
                    if norm1 > 0 and norm2 > 0:
                        cos_sim = np.dot(motion_vectors[i], motion_vectors[i+1]) / (norm1 * norm2)
                        cos_sims.append(cos_sim)
                if cos_sims:
                    motion_consistent = np.mean(cos_sims) > self.motion_consistency_threshold
        
        # 外观稳定性检查(如果提供了特征)
        appearance_stable = True
        if (appearance_feat is not None and 
            len(self.appearance_history[track_id]) > 0 and
            len(appearance_feat) > 0):
            
            last_feat = self.appearance_history[track_id][-1]
            if len(last_feat) == len(appearance_feat):
                try:
                    norm1 = np.linalg.norm(appearance_feat)
                    norm2 = np.linalg.norm(last_feat)
                    if norm1 > 0 and norm2 > 0:
                        cos_sim = np.dot(appearance_feat, last_feat) / (norm1 * norm2)
                        appearance_stable = cos_sim > self.appearance_sim_threshold
                    else:
                        appearance_stable = False
                except:
                    appearance_stable = False
            else:
                appearance_stable = False
            
        # 更新历史记录
        pos_history.append(current_pos)
        if len(pos_history) > self.stable_frames_threshold * 2:
            pos_history.pop(0)
            
        if appearance_feat is not None:
            self.appearance_history[track_id].append(appearance_feat)
            if len(self.appearance_history[track_id]) > 5:  # 保留最近5个外观特征
                self.appearance_history[track_id].pop(0)
                
        # 检查是否可以锁定ID
        if (len(pos_history) >= self.lock_after_frames and 
            pos_stable and appearance_stable and motion_consistent):
            self.locked_ids.add(track_id)
            if track_id in self.pending_ids:
                self.pending_ids.remove(track_id)
            
        return pos_stable and appearance_stable and motion_consistent

    def process_video(self):
        cap = cv2.VideoCapture(self.input_path)
        width, height = int(cap.get(3)), int(cap.get(4))
        self.set_valid_region(width)

        fps = cap.get(5)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
        
        # 创建显示窗口
        cv2.namedWindow('Real-time Tracking', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Real-time Tracking', width, height)

        while cap.isOpened():
            start_time = time.time()
            ret, frame = cap.read()
            if not ret: break

            # 绘制有效区域边界（黄色半透明矩形）
            overlay = frame.copy()
            cv2.rectangle(overlay,
                          (self.valid_region[0], 0),
                          (self.valid_region[1], height),
                          (0, 255, 255), -1)
            cv2.addWeighted(overlay, 0.1, frame, 0.9, 0, frame)

            # YOLOv8-Seg预测
            results = self.model.predict(
                source=frame,
                imgsz=640,
                conf=0.5,
                iou=0.5,
                device=self.device,
                verbose=False
            )

            detections = []
            masks_list = []
            for result in results:
                if result.masks is None:
                    continue

                boxes = result.boxes.xywh.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                clss = result.boxes.cls.cpu().numpy().astype(int)
                masks = result.masks.data.cpu().numpy()

                for box, conf, cls_id, mask in zip(boxes, confs, clss, masks):
                    if cls_id != 0:  # 只处理class_id=0的检测
                        continue
                    x_center = box[0]
                    if self.is_in_valid_region(x_center):
                        detections.append((box, conf, cls_id))
                        masks_list.append((mask, cls_id))

            # DeepSORT跟踪
            if detections:
                bboxes = torch.Tensor(np.array([d[0] for d in detections]))
                confs = torch.Tensor(np.array([d[1] for d in detections]))
                clss = torch.Tensor(np.array([d[2] for d in detections]))
                tracks = self.tracker.update(bboxes, confs, clss, frame)
            else:
                tracks = []

            # 处理跟踪结果
            valid_tracks = []
            for track in tracks:
                x1, y1, x2, y2, cls_id, track_id = map(int, track[:6])
                x_center = (x1 + x2) // 2
                y_center = (y1 + y2) // 2
                current_pos = (x_center, y_center)
                
                # 获取外观特征(如果可用)
                appearance_feat = track[6:] if len(track) > 6 else None
                
                # 调试日志
                debug_info = f"Track ID:{track_id} Pos:({x_center},{y_center})"
                if appearance_feat is not None:
                    debug_info += f" Feat_len:{len(appearance_feat)}"
                print(debug_info)

                if self.is_in_valid_region(x_center):
                    # 检查目标稳定性(位置+外观+运动)
                    is_stable = self.is_track_stable(track_id, current_pos, appearance_feat)
                    
                    # 更新统计信息（每个ID只统计一次）
                    if track_id not in self.recorded_ids and is_stable:
                        # 额外检查：确保不是短暂出现的虚假目标
                        if (track_id not in self.pending_ids and 
                            len(self.trajectories.get(track_id, [])) >= self.min_trajectory_length):
                            self.total_count += 1
                            self.recorded_ids.add(track_id)
                            valid_tracks.append(track)
                            print(f"New stable ID:{track_id} added to count")
                    
                    # 绘制有效目标
                    if is_stable or track_id in self.locked_ids:
                        valid_tracks.append(track)
            
            # 只处理有效目标
            for track in valid_tracks:
                x1, y1, x2, y2, cls_id, track_id = map(int, track[:6])
                print(f"Drawing box for ID:{track_id} at ({x1},{y1})-({x2},{y2})")  # 调试输出
                
                # 确保坐标有效
                if x1 >= x2 or y1 >= y2:
                    print(f"Invalid coordinates for ID:{track_id}")
                    continue
                    
                # 获取或生成颜色(BGR格式)
                if track_id not in self.color_dict:
                    self.color_dict[track_id] = (
                        int(np.random.randint(0, 255)),
                        int(np.random.randint(0, 255)), 
                        int(np.random.randint(0, 255))
                    )
                color = self.color_dict[track_id]

                # 绘制边界框(确保在frame范围内)
                h, w = frame.shape[:2]
                x1 = max(0, min(x1, w-1))
                y1 = max(0, min(y1, h-1))
                x2 = max(0, min(x2, w-1))
                y2 = max(0, min(y2, h-1))
                
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                cv2.putText(frame, f"ID:{track_id}", 
                           (x1, max(20, y1 - 10)),  # 确保文字不会超出图像顶部
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # 绘制分割掩膜
            overlay = frame.copy()
            for mask, cls_id in masks_list:
                color = [int(x) for x in np.random.randint(0, 255, 3)]
                mask = (mask > 0.5).astype(np.uint8)
                # 调整mask尺寸以匹配overlay
                if mask.shape[0] != overlay.shape[0] or mask.shape[1] != overlay.shape[1]:
                    mask = cv2.resize(mask, (overlay.shape[1], overlay.shape[0]))
                overlay[mask == 1] = color
            frame = cv2.addWeighted(overlay, self.alpha, frame, 1 - self.alpha, 0)

            # 显示统计信息和处理时间
            stats_x = width - 200
            text_color = (0, 255, 0)  # 绿色文字
            shadow_color = (0, 0, 0)  # 文字描边
            frame_time = time.time() - start_time
            
            # 绘制标题和帧率
            cv2.putText(frame, f"FPS: {1/frame_time:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, shadow_color, 3)
            cv2.putText(frame, f"FPS: {1/frame_time:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 2)
            
            # 绘制统计信息
            cv2.putText(frame, "Total Count:", (stats_x, 40),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, shadow_color, 3)
            cv2.putText(frame, "Total Count:", (stats_x, 40),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 2)
            cv2.putText(frame, f"{self.class_names[0]}: {self.total_count}",
                        (stats_x, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.6, shadow_color, 3)
            cv2.putText(frame, f"{self.class_names[0]}: {self.total_count}",
                        (stats_x, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.6, text_color, 2)

            # 实时显示并检查窗口状态
            if frame is not None and frame.size != 0:
                cv2.imshow('Real-time Tracking', frame)
                if cv2.waitKey(1) & 0xFF == ord('q'):  # 按q键退出
                    break
            else:
                print("Warning: Empty frame detected!")

            out.write(frame)

        cap.release()
        out.release()


if __name__ == "__main__":
    config = {
        "input": "视频/699b2d9941c677a2fc210350cce01c53.mp4",
        "output": "output2/result_class0_improved.mp4",
        "weights": "weights/best.pt"  # 确保使用实例分割模型权重
    }

    tracker = YOLOv8SegDeepSORT(config)
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 跟踪启动")
    tracker.process_video()
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 跟踪完成")
    print("最终统计结果:")
    print(f"{tracker.class_names[0]}: {tracker.total_count}")
    cv2.destroyAllWindows()